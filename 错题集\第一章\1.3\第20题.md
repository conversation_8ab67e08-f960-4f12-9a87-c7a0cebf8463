# 题目：

假定编译器对高级语言的某条语句可以编译生成两种不同的指令序列, A、B 和 C 三类指令的 CPI 和两种不同序列所含的三类指令条数如下表所示, 两个指令序列都在时钟周期为 2ns 的机器上运行, 则下列结论中正确的是 ( )。

| 指令类型 | CPI | 序列一的指令条数 | 序列二的指令条数 |
| :--- | :-: | :---: | :---: |
| A | 1 | 2 | 1 |
| B | 2 | 1 | 2 |
| C | 3 | 4 | 2 |

A. 序列一的 MIPS 数比序列二多 50, 序列一的执行速度比序列二快 10ns
B. 序列一的 MIPS 数比序列二多 50, 序列二的执行速度比序列一快 10ns
C. 序列二的 MIPS 数比序列一多 50, 序列一的执行速度比序列二快 10ns
D. 序列二的 MIPS 数比序列一多 50, 序列二的执行速度比序列一快 10ns

# 我的答案：



# 标准答案：

D

MIPS = 主频÷(CPI×10^6)，主频 = 1/时钟周期 = 1/2ns = 500MHz，序列一的 CPI = (1×1 + 1×2 + 4×3)÷6 = 15÷6 = 2.5，序列二的 CPI = (2×1 + 1×2 + 2×3)÷5 = 10÷5 = 2，故序列一的 MIPS = 500×10^6÷(2.5×10^6) = 200，序列二的 MIPS = 500×10^6÷(2×10^6) = 250。CPU 执行时间 = 指令条数×CPI×时钟周期 = 程序的时钟周期数×时钟周期，序列一所需的时钟周期数是 15，序列二所需的时钟周期数是 10，所以序列一的执行时间为 15×2ns = 30ns，序列二的执行时间为 10×2ns = 20ns。
