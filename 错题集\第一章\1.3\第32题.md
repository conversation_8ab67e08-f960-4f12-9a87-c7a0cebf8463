# 题目：

【2023 统考真题】若机器 M 的主频为 1.5GHz, 在 M 上执行程序 P 的指令条数为 $5 \times 10^5$, P 的平均 CPI 为 1.2, 则 P 在 M 上的指令执行速度和用户 CPU 时间分别为 ( )。
A. 0.8GIPS, 0.4ms
B. 0.8GIPS, 0.4μs
C. 1.25GIPS, 0.4ms
D. 1.25GIPS, 0.4μs

# 我的答案：



# 标准答案：

C

程序 P 的指令条数为 5×10^5，平均 CPI 为 1.2，程序 P 的总时钟周期数为 5×10^5×1.2 = 6×10^5，主频 1.5GHz 说明 1s 有 1.5G = 1.5×10^9 个时钟周期。因此，指令执行速度 = 主频/平均 CPI = 1.5×10^9/1.2 = 1.25GIPS，用户 CPU 时间 = 6×10^5÷(1.5×10^9)s = 4×10^-4s = 0.4ms。
