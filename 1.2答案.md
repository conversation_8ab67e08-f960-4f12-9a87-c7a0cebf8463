好的，这是从您提供的三张图片中提取并按序号排列的文字：

**第1章 计算机系统概述**

**单项选择题**

**01. D**
选项A是计算机主机的组成部分，而选项B、C只涉及计算机系统的部分内容，都不完整。

**02. A**
冯·诺依曼机的基本工作方式是按制流驱动方式，也就是按照指令的执行序列，依次读取指令，然后根据指令所含的控制信息，调用数据信息进行处理。因此，在执行程序的过程中，始终以控制信息流为驱动工作的因素，而数据信息流则是被动地被调用处理。

**03. B**
选项 A 是不存在的机器，选项 B 是对“存储程序”的阐述，因此正确。选项 C 是与题干无关的选项。选项 D 是相联存储器的特点。

**04. D**

**05. D**
IR 存放当前执行的指令代码，PC 存放下一条指令的地址，不要将它们混淆。此外，MAR 用来存放待访问的存储单元地址，MDR 用来存放被写入存储单元或从存储单元读出的数据。

**06. A**
在 CPU 中，PC 用来跟踪下一条要执行的指令在主存储器中的地址。

**07. C**
地址译码器是主存储器的构成部分，不属于 CPU。地址寄存器虽然一般属于主存储器，但现代计算机中绝大多数 CPU 内集成了地址寄存器。

**08. A**
地址寄存器（MAR）存放访存地址，因此位数与地址码长度相同。数据寄存器（MDR）用于暂存要从存储器中读或写的信息，因此位数与存储字长相同。

**09. D**
运算器的核心是 ALU。地址寄存器位于 CPU 内，但并不是集成在运算器中。地址寄存器用来保存当前 CPU 所访问的内存单元的地址。因为内存和 CPU 之间存在着操作速度上的差别，所以必须使用地址寄存器来保持地址信息，直到内存的读/写操作完成为止。

**10. C**
寄存器在 CPU 内部，速度最快。Cache 采用高速的 SRAM 制作，而内存需用 DRAM 制作，其速度较 Cache 慢。本题也可根据存储器层次结构的速度关系得出答案。

**11. C**
8 位计算机表明计算机字长为 8 位，即一次可以处理 8 位的数据；而 16 位表示地址码的长度，因此该机器有 $2^{16}=65536$ 个地址空间。

**12. B**
计算机只能从主存储器中取指令与操作数，不能直接与外存交换数据。

**13. C**
编译程序是先完整编译后运行的程序，如 C、C++ 等；解释程序是逐句翻译且边翻译边执行的程序，如 JavaScript、Python 等。解释程序要边翻译成机器语言边执行，因此一般速度较编译程序慢。为增加对该过程的理解，附 C 语言编译链接的过程：
源程序(.c) —(编译)—> 汇编语言源程序 —(汇编程序)—> 可重定位目标文件 —(链接程序)—> 可执行文件

**14. C**
机器语言是计算机唯一可以直接执行的语言，汇编语言用助记符编写，以便记忆。而正则语言是编译原理中符合正则文法的语言。

**15. D**
解释程序的特点是翻译一句执行一句，边翻译边执行；由高级语言转化为汇编语言的过程称为编译，把汇编语言源程序翻译成机器语言程序的过程称为汇编。

**16. D**
在不同的设备中，汇编语言对应着不同的机器语言指令集，通过汇编程序转换成机器指令。特定的汇编语言与特定的机器语言指令集是一一对应的，不同平台之间不可直接移植。

**17. B**
计算机只能直接执行机器指令，而汇编指令需要通过汇编程序转换成机器指令才能被计算机直接执行。

**18. C**
全面代表计算机性能的是实际软件的运行情况。向后兼容是指时间上向后兼容，即新机器兼容使用以前机器的指令系统。软件和硬件在逻辑功能上是等价的，如浮点运算即可以用专门的浮点运算器实现，也可以通过编写一段子程序实现。

**19. B**
CPU 由运算器和控制器两个部件组成，而运算器和控制器中都含有寄存器。存储器是一个独立的部件。

**20. C**
相联存储器既可以按地址寻址又可以按内容（通常是某些字段）寻址，为与传统存储器区别，也称按内容寻址的存储器。

**21. A**
硬件能直接执行的只能是机器语言（二进制编码），汇编语言是增强机器语言的可读性和记忆性的语言，经过汇编后才能被执行。

**22. C**
翻译程序是指把高级语言源程序转换成机器语言程序的软件。翻译程序有两种：一种是编译程序，它将源程序一次全部翻译成目标程序，并且会生成目标代码文件。另一种是解释程序，它将源程序的一条语句翻译成对应的机器目标代码，并立即执行，翻译一句执行一句，并且不会生成目标代码文件。汇编程序也是一种翻译程序，它把汇编语言源程序翻译为机器语言程序。

**23. C**
冯·诺依曼结构计算机的功能部件包括输入设备、输出设备、存储器、运算器和控制器。程序的功能都通过中央处理器（运算器和控制器）执行指令，选项 A 正确。指令和数据以同等地位存放于存储器内，形式上无差别，只在程序执行时具有不同的含义，选项 B 正确。指令按地址访问，数据由指令的地址码指出，除立即寻址外，数据均存放在存储器内，选项 C 错误。在程序执行前，指令和数据需预先存放在存储器中，中央处理器可以从存储器存取代码，选项 D 正确。

**24. A**
将源程序转换为可执行目标文件的过程分为预处理、编译、汇编、链接四个阶段。