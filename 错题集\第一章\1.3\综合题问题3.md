# 题目：

某台计算机只有 LOAD/STORE 指令能对存储器进行读/写操作, 其他指令只对寄存器进行操作。根据程序跟踪试验结果, 已知每条指令所占的比例及 CPI 数如下表所示。
求上述情况下的平均 CPI。

| 指令类型 | 指令所占比例 | CPI |
| :--- | :---: | :-: |
| 算术逻辑指令 | 43% | 1 |
| LOAD 指令 | 21% | 2 |
| STORE 指令 | 12% | 2 |
| 转移指令 | 24% | 2 |

**[附加问题]**
假设程序由 M 条指令组成。算术逻辑运算中 25% 的指令的两个操作数中的一个已在寄存器中, 另一个必须在算术逻辑指令执行前用 LOAD 指令从存储器中取到寄存器中。因此有人建议增加另一种算术逻辑指令, 其特点是一个操作数取自寄存器, 另一个操作数取自存储器, 即寄存器-存储器类型, 假设这种指令的 CPI 等于 2。同时, 转移指令的 CPI 变为 3。求新指令系统的平均 CPI。

# 我的答案：



# 标准答案：

① 本处理机共包含 4 种指令，则 CPI 就是这 4 种指令的数学期望，即
CPI = 1×43% + 2×21% + 2×12% + 2×24% = 1.57

② 设原指令总数为 M，因为新增的算术操作有取操作数的功能，替代了 LOAD 的功能，所以新指令总数为
M + (0.25×0.43)M - (0.25×0.43)M - (0.25×0.43)M = 0.8925M
增加另一种算术逻辑指令后，每种指令所占的比例及 CPI 如下表所示：

| 指令类型 | 指令所占比例 | CPI |
| :--- | :--- | :--- |
| 算术逻辑指令 | (0.43M-0.43M×0.25)/0.8925M=0.3613 | 1 |
| 算术逻辑指令(新) | (0.43M×0.25)/0.8925M=0.1204 | 2 |
| LOAD 指令 | (0.21M-0.43M×0.25)/0.8925M=0.1149 | 2 |
| STORE 指令 | 0.12M/0.8925M=0.1345 | 2 |
| 转移指令 | 0.24M/0.8925M=0.2689 | 3 |

所以 CPI' = 1×0.3613 + 2×0.1204 + 2×0.1149 + 2×0.1345 + 3×0.2689 = 1.9076。
