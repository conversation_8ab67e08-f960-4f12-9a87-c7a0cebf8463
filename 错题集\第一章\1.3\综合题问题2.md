# 题目：

微机 A 和 B 是采用不同主频的 CPU 芯片, 片内逻辑电路完全相同。
1) 若 A 机的 CPU 主频为 8MHz, B 机为 12MHz, 则 A 机的 CPU 时钟周期为多少?
2) 若 A 机的平均指令执行速度为 0.4MIPS, 则 A 机的平均指令周期为多少?
3) B 机的平均指令执行速度为多少?

# 我的答案：



# 标准答案：

1) A 机的 CPU 主频为 8MHz，所以 A 机的 CPU 时钟周期 = 1÷8MHz = 0.125μs。
A 机的平均指令周期 = 1÷0.4MIPS = 2.5μs。
A 机平均每条指令的时钟周期数 = 2.5μs÷0.125μs = 20。
因微机 A 和 B 的片内逻辑电路完全相同，所以 B 机平均每条指令的时钟周期数也为 20。
因为 B 机的 CPU 主频为 12MHz，所以 B 机的 CPU 时钟周期 = 1÷12MHz = 1/12μs。
B 机的平均指令周期 = 20×(1/12) = 5/3μs。
B 机的平均指令执行速度 = 1÷(5/3)μs = 0.6MIPS。
【另解】B 机的平均指令执行速度 = A 机的平均指令执行速度×(12/8) = 0.4MIPS×(12/8) = 0.6MIPS。
