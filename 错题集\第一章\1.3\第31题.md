# 题目：

【2022 统考真题】某计算机主频为 1GHz, 程序 P 运行过程中, 共执行了 10000 条指令, 其中, 80% 的指令执行平均需 1 个时钟周期, 20% 的指令执行平均需 10 个时钟周期。程序 P 的平均 CPI 和 CPU 执行时间分别是 ( )。
A. 2.8, 28μs
B. 28, 28μs
C. 2.8, 28ms
D. 28, 28ms

# 我的答案：



# 标准答案：

A

CPI 指平均每条指令的执行需要多少个时钟周期。80%的指令执行平均需要 1 个时钟周期，20%的指令执行平均需要 10 个时钟周期，因此 CPI = 80%×1 + 20%×10 = 2.8。计算机主频为 1GHz，程序 P 共执行 10000 条指令，平均每条指令需要 2.8 个时钟周期，因此，CPU 执行时间 = (10000×2.8)×10^-9 = 2.8×10^-5s = 28μs。
