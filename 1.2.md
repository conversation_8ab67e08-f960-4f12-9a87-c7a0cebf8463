您好，非常抱歉我之前的回复中可能存在错误。感谢您的指正！

我已经重新、仔细地检查了您提供的所有图片，并对提取的文字进行了逐一核对和修正。以下是根据图片内容重新整理和校对后的文字，按序号排列：

---

### **第1章 计算机系统概述**

**1.2.6 本节习题精选**

**单项选择题**
1.  完整的计算机系统应包括（ ）。
    A. 运算器、存储器、控制器
    B. 外部设备和主机
    C. 主机和应用程序
    D. 配套的硬件设备和软件系统

2.  冯·诺依曼机的基本工作方式是（ ）。
    A. 控制流驱动方式
    B. 多指令多数据流方式
    C. 微程序控制方式
    D. 数据流驱动方式

3.  下列（ ）是冯·诺依曼机工作方式的基本特点。
    A. 多指令流多数据流
    B. 按地址访问并顺序执行指令
    C. 堆栈操作
    D. 存储器按内容选择地址

4.  以下说法错误的是（ ）。
    A. 硬盘是外部设备
    B. 软件的功能与硬件的功能在逻辑上是等价的
    C. 硬件实现的功能一般比软件实现具有更高的执行速度
    D. 软件的功能不能用硬件取代

5.  存放当前执行指令的寄存器是（ ）。
    A. MAR
    B. PC
    C. MDR
    D. IR

6.  在CPU中，跟踪下一条要执行的指令的地址的寄存器是（ ）。
    A. PC
    B. MAR
    C. MDR
    D. IR

7.  CPU不包括（ ）。
    A. 地址寄存器
    B. 指令寄存器 (IR)
    C. 地址译码器
    D. 通用寄存器

8.  MAR和MDR的位数分别为（ ）。
    A. 地址码长度、存储字长
    B. 存储字长、存储字长
    C. 地址码长度、地址码长度
    D. 存储字长、地址码长度

9.  在运算器中，不包含（ ）。
    A. 状态寄存器
    B. 数据总线
    C. ALU
    D. 地址寄存器

10. 下列关于CPU存取速度的比较中，正确的是（ ）。
    A. Cache > 内存 > 寄存器
    B. Cache > 寄存器 > 内存
    C. 寄存器 > Cache > 内存
    D. 寄存器 > 内存 > Cache

11. 若一个8位的计算机系统以16位来表示地址，则该计算机系统有（ ）个地址空间。
    A. 256
    B. 65535
    C. 65536
    D. 131072

12. C语言程序是在（ ）中运行的，包括所需的数据。
    A. 数据通路
    B. 主存
    C. 硬盘
    D. 操作系统

13. 关于编译程序和解释程序，下列说法中错误的是（ ）。
    A. 编译程序和解释程序的作用都是将高级语言程序转换成机器语言程序
    B. 编译程序编译时间较长，运行速度较快
    C. 解释程序方法较简单，运行速度也较快
    D. 解释程序将源程序翻译成机器语言，并且翻译一条以后，立即执行这条语句

14. 可以不经编译和汇编，由计算机直接执行的语言是（ ）。
    I. 机器语言 II. 汇编语言 III. 高级语言 IV. 操作系统 V. 正则语言
    A. II、III
    B. II、IV
    C. I、II
    D. I、V

15. 只有当程序执行时才将源程序翻译成机器语言，并且一次只能翻译一行语句，边翻译边执行的是（ ）程序。把汇编语言源程序转变为机器语言程序的过程是（ ）。
    I. 编译 II. 目标 III. 汇编 IV. 解释
    A. I、II
    B. IV、II
    C. IV、III
    D. IV、I

16. 下列关于各种级别语言的描述中，错误的是（ ）。
    A. 可用高级语言和低级语言编写出功能等价的程序
    B. 低级语言的执行效率一般情况下高于高级语言
    C. 机器语言程序可直接在机器上执行，而高级语言和汇编语言程序不可以
    D. 汇编语言与机器结构无关

---

17. 下列关于机器指令和汇编指令的叙述中，错误的是（ ）。
    A. 可以直接用机器语言（机器指令）编写程序
    B. 汇编指令和机器指令都能被计算机直接执行
    C. 汇编语言和机器语言都与计算机系统结构相关
    D. 汇编指令和机器指令一一对应，功能相同

18. 下列叙述中，正确的是（ ）。
    I. 实际应用程序的测试结果能够全面代表计算机的性能
    II. 系列机的基本特性是指令系统向后兼容
    III. 软件和硬件在逻辑功能上是等价的
    A. I
    B. III
    C. II 和 III
    D. I、II 和 III

19. 在CPU的组成中，不包括（ ）。
    A. 运算器
    B. 存储器
    C. 控制器
    D. 寄存器

20. 关于相联存储器，下列说法中正确的是（ ）。
    A. 只可以按地址寻址
    B. 只可以按内容寻址
    C. 既可按地址寻址又可按内容寻址
    D. 以上说法均不完善

21. **【2015统考真题】** 计算机硬件能够直接执行的是（ ）。
    I. 机器语言程序 II. 汇编语言程序 III. 硬件描述语言程序
    A. 仅I
    B. 仅 I、II
    C. 仅 I、III
    D. I、II、III

22. **【2016统考真题】** 将高级语言源程序转换为机器级目标代码文件的程序是（ ）。
    A. 汇编程序
    B. 链接程序
    C. 编译程序
    D. 解释程序

23. **【2019统考真题】** 下列关于冯·诺依曼计算机基本思想的叙述中，错误的是（ ）。
    A. 程序的功能都通过中央处理器执行指令实现
    B. 指令和数据都用二进制数表示，形式上无差别
    C. 指令按地址访问，数据都在指令中直接给出
    D. 程序执行前，指令和数据需预先存放在存储器中

24. **【2022统考真题】** 将高级语言源程序转换为可执行目标文件的主要过程是（ ）。
    A. 预处理→编译→汇编→链接
    B. 预处理→汇编→编译→链接
    C. 预处理→编译→链接→汇编
    D. 预处理→汇编→链接→编译
